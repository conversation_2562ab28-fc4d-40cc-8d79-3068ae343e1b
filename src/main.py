import logging
import threading
from datetime import date, timedelta

from flask import Flask, jsonify

from aws import turn_off_instance
from environment import get_aws_config
from task_runner import run_tasks

app = Flask(__name__)
logging.getLogger('werkzeug').setLevel(logging.ERROR)


def run_daily_views_and_shutdown():
    run_tasks()
    shutdown_instance()


def shutdown_instance():
    aws_config = get_aws_config()
    logger.info("Turning off instance...")
    turn_off_instance(**aws_config)


def setup_logging(quiet=False):
    """Configure logging based on mode"""
    level = logging.ERROR if quiet else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


# Configure global logger
logger = setup_logging()


@app.route("/health", methods=["GET"])
def health_check():
    return jsonify({"status": "ok"}), 200


if __name__ == "__main__":
    task_thread = threading.Thread(target=run_daily_views_and_shutdown)
    task_thread.daemon = True
    task_thread.start()

    app.run(host='0.0.0.0', port=8080, debug=False)
