from database_task_executor import DatabaseTask
from datetime import date



def create_rent_monthly_tasks(date_str: str) -> list[DatabaseTask]:
    """Create database tasks for a specific month."""
    return [
        DatabaseTask('refresh_materialize_view_concurrently', f'effective_rent_by_{date_str}'),
        DatabaseTask('refresh_materialize_view_concurrently', f'rent_listing_by_{date_str}'),
    ]


def create_summary_monthly_tasks(date_str: str) -> list[DatabaseTask]:
    """Create database tasks for a specific month."""
    return [
        DatabaseTask('refresh_materialize_view_concurrently', f'rent_summary_by_{date_str}'),
        DatabaseTask('refresh_materialize_view_concurrently', f'rent_bedroom_summary_by_{date_str}'),
    ]


def create_availability_task() -> DatabaseTask:
    """Create database tasks for a specific month."""
    return DatabaseTask('refresh_materialize_view_concurrently_without_date_of_record', 'bedroom_future_availability')


def create_views(current_date: date) -> DatabaseTask:
    return DatabaseTask('create_monthly_rent_views', current_date)
