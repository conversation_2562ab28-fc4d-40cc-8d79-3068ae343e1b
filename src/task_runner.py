from datetime import timedelta, date

from environment import setup_environment, get_db_config
from database_task_executor import execute_database_tasks
from task_definition import *


def run_tasks():
    """Execute daily materialized view refresh tasks and shutdown the instance."""

    setup_environment()
    db_config = get_db_config()

    postgres_client = PostgreSQLClient(**db_config)
    rent_listing_by_tasks = get_tasks_to_run(db_config, 'rent_listing_by_%')
    effective_rent_by_tasks = get_tasks_to_run(db_config, 'effective_rent_by_%')
    for task in tasks:
        execute_database_tasks([task], db_config)

    # Always refresh current month views and bedroom availability
    execute_database_tasks(create_rent_monthly_tasks(current_date_str) + [create_availability_task()], db_config)
    execute_database_tasks(create_summary_monthly_tasks(current_date_str), db_config)

    # Refresh previous month views only if it's a different month
    if seven_days_before_str != current_date_str:
        execute_database_tasks(create_rent_monthly_tasks(seven_days_before_str), db_config)
        execute_database_tasks(create_summary_monthly_tasks(seven_days_before_str), db_config)

    # Create views for future months
    execute_database_tasks([create_views(current_date)], db_config)



def get_tasks_to_run(db_config, view_prefix, limit):
    query = """select view_name,
    from rent_summarized_records_status
    where ( last_date_of_record is null
       or (last_source_data_update is not null and last_source_data_update > updated_at) )
        and view_name like 'view_prefix'
        order by last_date_of_record desc
        limit limit;
    """
