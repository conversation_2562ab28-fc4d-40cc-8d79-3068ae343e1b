from datetime import timed<PERSON><PERSON>, date

from environment import setup_environment, get_db_config
from database_task_executor import execute_database_tasks
from src.postgres_runner import PostgreSQLClient
from task_definition import *


def run_tasks():
    """Execute daily materialized view refresh tasks and shutdown the instance."""

    setup_environment()
    db_config = get_db_config()
    with PostgreSQLClient(**db_config) as postgres_client:
        rent_listing_by_tasks = get_tasks_to_run(postgres_client, 'rent_listing_by_%', 12)
        effective_rent_by_tasks = get_tasks_to_run(postgres_client, 'effective_rent_by_%', 12)
        for task in tasks:
            execute_database_tasks([task], postgres_client)

        # Create views for future months
        execute_database_tasks([create_views(current_date)], db_config)



def get_tasks_to_run(postgres_client, view_prefix, limit):
    query = """select view_name
    from rent_summarized_records_status
    where ( last_date_of_record is null
       or (last_source_data_update is not null and last_source_data_update > updated_at) )
        and view_name like %s
        order by last_date_of_record desc
        limit %s;
    """

    result = postgres_client.execute_query(query, (view_prefix, limit))
    return [row[0] for row in result]
