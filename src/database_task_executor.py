import logging
from dataclasses import dataclass
from typing import List
from concurrent.futures import ThreadPoolExecutor, as_completed
from postgres_runner import PostgreSQLClient
from scheduler import execute_procedure_with_connection
from datetime import date

logger = logging.getLogger(__name__)


@dataclass
class DatabaseTask:
    procedure: str
    param: str | date

def execute_database_tasks(tasks: List[DatabaseTask], postgres_client: PostgreSQLClient):

    """Execute database tasks with proper error handling"""
    logger.info(f"Executing {len(tasks)} database tasks...")

    with ThreadPoolExecutor(max_workers=2) as executor:
        # Submit all tasks at once
        future_to_task = {
            executor.submit(
                execute_procedure_with_connection,
                postgres_client,
                task.procedure,
                task.param,
                True
            ): task for task in tasks
        }

        for future in as_completed(future_to_task):
            task = future_to_task[future]
            try:
                result = future.result()
                logger.info(f"Success: {task.procedure} with param: {task.param}")
            except Exception as e:
                logger.error(f"Failed: {task.procedure} with param: {task.param} - {e}")